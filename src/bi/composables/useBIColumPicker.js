import { uniqBy } from 'lodash-es';

export function useBIColumnPicker(props, type = 'filters') {
  const getPreviousStageFields = () => props.previousStageFields;

  const getCurrentStageFields = () => props.fields.map(field => ({ ...field, type: field.field_type, label: field.alias, field: field.alias }));

  const getVisibleFields = fn => fn().filter(field => !field.skip);

  const getCurrentStageHiddenFieldsMap = () => getCurrentStageFields().reduce((acc, field) => {
    if (field.skip)
      acc[field.alias] = true;
    return acc;
  }, {});

  const filterPreviousFieldsBasedOnCurrentStage = fn => fn().filter(field => !getCurrentStageHiddenFieldsMap()[field.alias]);

  const getPreviousStageVisibleFields = () => filterPreviousFieldsBasedOnCurrentStage(getPreviousStageFields);

  const getCurrentStageVisibleFields = () => getVisibleFields(getCurrentStageFields);

  const getAllFields = () => uniqBy([...getPreviousStageFields(), ...getCurrentStageFields()], 'alias');

  const getAllVisibleFields = () => uniqBy([...getPreviousStageVisibleFields(), ...getCurrentStageVisibleFields()], 'alias');

  const filterOptions = () => [{ label: `Stage ${props.stageIndex + 1}`, columns: getAllVisibleFields(), tables: props.tables }];

  const sortOptions = () => [{ label: `Stage ${props.stageIndex + 1}`, columns: getAllVisibleFields(), tables: [] }];

  const columnPickerOptions = () => [{ label: `Stage ${props.stageIndex + 1}`, columns: getPreviousStageFields(), tables: props.tables }];

  const joinsOptions = () => [{ label: `Stage ${props.stageIndex + 1}`, columns: getPreviousStageFields(), tables: props.tables }];

  const windowFunctionOptions = () => [{ label: `Stage ${props.stageIndex + 1}`, columns: getAllVisibleFields(), tables: [] }];

  const expressionEditorOptions = () => uniqBy([
    ...props.tables.map(table => table.columns.map(column => ({ label: column.alias, name: column.field, type: column.type, path: column.path }))).flat(),
    ...getAllFields().map(field => ({
      label: field.alias || field.label,
      is_aggregation: field.is_aggregation,
      type: field.type,
    })),
  ], 'label');

  const options_map = {
    filters: filterOptions,
    sort: sortOptions,
    column_picker: columnPickerOptions,
    joins: joinsOptions,
    expression: expressionEditorOptions,
    settings: windowFunctionOptions,
  };

  const stages = computed(options_map[type]);

  return { stages, columns: stages };
}
