<script setup>
import { useBIColumnPicker } from '~/bi/composables/useBIColumPicker';
import { useCommonImports } from '~/common/composables/common-imports.composable';

const props = defineProps({
  tables: {
    type: Array,
    default: () => [],
  },
  stageIndex: {
    type: Number,
  },
  previousStageFields: {
    type: Array,
    default: () => [],
  },
  fields: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['selected', 'expression']);
const { $t } = useCommonImports();

function onColumnsSelected(field) {
  emit('selected', field);
}

function onExpressionClicked(field) {
  emit('expression', field);
}

const { stages } = useBIColumnPicker(props, 'sort');

const dropdown = ref(null);
function updateDropdownPosition() {
  if (dropdown.value) {
    nextTick(() => {
      dropdown.value.setFixedPosition();
    });
  }
}
</script>

<template>
  <hawk-menu ref="dropdown" position="fixed" additional_trigger_classes="!ring-0 !focus:ring-0 !px-0 !py-0" additional_dropdown_classes="transition duration-150 ease-in-out">
    <template #trigger="{ open }">
      <div>
        <slot :open="open">
          <hawk-button type="text">
            <IconHawkPlus />  {{ $t('Add columns') }}
          </hawk-button>
        </slot>
      </div>
    </template>
    <template #content>
      <bi-query-builder-column-picker
        :stages="stages" :show-stage-header="false"
        :is-dropdown="true" :has-functions="false" :stage-index="stageIndex" @selected="onColumnsSelected($event);updateDropdownPosition()" @expression="onExpressionClicked($event);updateDropdownPosition()"
      />
    </template>
  </hawk-menu>
</template>
