<script setup>
import { useBIColumnPicker } from '~/bi/composables/useBIColumPicker';
import { useCommonImports } from '~/common/composables/common-imports.composable';

const props = defineProps({
  tables: {
    type: Array,
    default: () => [],
  },
  previousStageFields: {
    type: Array,
    default: () => [],
  },
  stageIndex: {
    type: Number,
  },
});

const emit = defineEmits(['selected']);
const { $t } = useCommonImports();
const is_dropdown_open = ref(false);

const { stages } = useBIColumnPicker(props, 'joins');

function onColumnsSelected(field) {
  emit('selected', field);
  is_dropdown_open.value = false;
}
</script>

<template>
  <hawk-menu position="fixed" additional_trigger_classes="!ring-0 !focus:ring-0 !px-0 !py-0 w-full text-left">
    <template #trigger>
      <div>
        <div class=" border rounded-lg p-2 flex items-center justify-between cursor-pointer" :class="{ 'bg-primary-25 border-primary-200 text-primary-500': !$slots.default }">
          <div>
            <slot>
              <div class="text-xs">
                {{ $t('Choose a column') }}
              </div>
              <div> - </div>
            </slot>
          </div>
          <div>
            <IconHawkChevronDown class="size-4 transition-transform" :class="{ 'rotate-180': is_dropdown_open }" />
          </div>
        </div>
      </div>
    </template>
    <template #content="{ close }">
      <bi-query-builder-column-picker :stages="stages" :is-dropdown="true" :has-functions="false" :stage-index="stageIndex" @selected="onColumnsSelected($event);close()" @expression="onExpressionClicked" />
    </template>
  </hawk-menu>
</template>
