<script setup>
import { useBIColumnPicker } from '~/bi/composables/useBIColumPicker';
import { useCommonImports } from '~/common/composables/common-imports.composable';

const props = defineProps({
  tables: {
    type: Array,
    default: () => [],
  },
  stageIndex: {
    type: Number,
  },
  fields: {
    type: Array,
    default: () => [],
  },
  previousStageFields: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['selected', 'expression']);
const { $t } = useCommonImports();
const { stages } = useBIColumnPicker(props, 'column_picker');

function onColumnsSelected(field) {
  emit('selected', field);
}

function onExpressionClicked(field) {
  emit('expression', field);
}

const dropdown = ref(null);
function updateDropdownPosition() {
  if (dropdown.value) {
    nextTick(() => {
      dropdown.value.setFixedPosition();
    });
  }
}
</script>

<template>
  <hawk-menu ref="dropdown" position="fixed" additional_trigger_classes="!ring-0 !focus:ring-0 !px-2 !py-0" additional_dropdown_classes="transition duration-150 ease-in-out">
    <template #trigger>
      <Hawk-button type="link">
        <IconHawkPlus />   {{ $t('Add columns') }}
      </Hawk-button>
    </template>
    <template #content>
      <bi-query-builder-column-picker
        v-if="stageIndex === 0"
        :tables="tables" :stage-index="stageIndex" @selected="onColumnsSelected($event);updateDropdownPosition()" @expression="onExpressionClicked($event);updateDropdownPosition()"
      />
      <bi-query-builder-column-picker v-else :stages="stages" :stage-index="stageIndex" @selected="onColumnsSelected($event);updateDropdownPosition()" @expression="onExpressionClicked($event);updateDropdownPosition()" />
    </template>
  </hawk-menu>
</template>
