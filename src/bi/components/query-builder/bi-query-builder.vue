<script setup>
import { watchDebounced } from '@vueuse/core';
import DOMPurify from 'dompurify';
import { cloneDeep, uniqBy } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';
import { useBiStore } from '~/bi/store/bi.store';
import { useCommonImports } from '~/common/composables/common-imports.composable';

const { $t } = useCommonImports();
const bi_store = useBiStore();

const { widget_builder_config, all_schemas, all_schema_columns } = storeToRefs(bi_store);
const { stages } = toRefs(widget_builder_config.value.query);

const { getIconsForType, createDefaultSelection, constructFieldName } = useBIQueryBuilder(stages, all_schema_columns);

const join_types = getIconsForType('joins');

const is_add_stage_disabled = computed(() => stages.value[stages.value.length - 1]?.columns?.filter(col => col?.is_aggregation)?.length === 0);

watchDebounced(() => stages.value, () => {
  bi_store.config_change_detection_counter++;
}, { deep: true, debounce: 100 });

function addStage() {
  stages.value.push(createDefaultSelection());
}

function getTablesFromStageFields(stage_index) {
  const fieldData = field => ({
    ...field,
    label: field.alias,
    type: field.field_type,
    field: field.alias,
    alias: field.alias,
  });

  const expressionData = field => ({
    ...field,
    label: field.alias,
    type: field.field_type,
    field: field.alias,
    alias: field.alias,
  });

  const previous_stage_columns = {};
  stages.value.forEach((stage, index) => {
    if (index < stage_index) {
      stage.columns.forEach((column) => {
        if (column.skip)
          delete previous_stage_columns[column.alias];
        else
          previous_stage_columns[column.alias] = column;
      });
    }
  });

  const columns = uniqBy(Object.values(previous_stage_columns).map(field => ({
    ...(field.expr ? expressionData(field) : fieldData(field)),
  })), 'alias');

  const tables = [{
    label: ``,
    name: '',
    columns,
  }];
  return tables;
}

function removeSelectionFromStage(stage, stage_index) {
  const tables = getStageTables(stage_index);
  const column_alias = tables.map(table => table.columns.map(column => column.alias)).flat();
  const column_fields = tables.map(table => table.columns.map(column => column.field)).flat();

  getStagePreviousFields(stage_index).forEach((column) => {
    column_alias.push(column.alias);
    column_fields.push(constructFieldName({ label: column.alias }, 'results'));
  });

  // Filter selected fields
  let field_alias = stage.columns.map(field => field.alias);
  const is_includes = f => column_alias.includes(f) || field_alias.includes(f);

  if (stage.columns?.length) {
    const original = stage.columns.slice();
    const current = original.slice();

    const removeAlias = (alias) => {
      field_alias = field_alias.filter(f => f !== alias);
    };

    for (let i = current.length - 1; i >= 0; i--) {
      const field = current[i];
      if (!field.expr) {
        const keep = column_alias.includes(field.alias) || column_fields.includes(field.field);
        if (!keep) {
          current.splice(i, 1);
          removeAlias(field.alias);
        }
      }
    }
    for (let i = current.length - 1; i >= 0; i--) {
      const field = current[i];
      if (field.expr) {
        const refs = field.fields.map(f => f.split(' > ').pop());
        const keep = refs.every(is_includes);
        if (!keep) {
          current.splice(i, 1);
          removeAlias(field.alias);
        }
      }
    }

    // Preserve original ordering of remaining fields
    stage.columns = current;
  }

  // Update the selection fields with the new filtered fields
  field_alias = stage.columns.map(field => field.alias);

  // Update orderBy fields
  if (stage.orderBy?.length)
    stage.orderBy = stage.orderBy.filter(field => field_alias.includes(field.column));
}

function filterPreviousTables(joins, stage_index) {
  const current_table_columns = getStagePreviousFields(stage_index).map(column => column.field);
  const getConditions = join => join.on?.reduce((acc, condition) => {
    acc.push(condition.left);
    return acc;
  }, []) || [];
  return joins.filter(join => getConditions(join).every(column => current_table_columns.includes(column)));
}

function syncPipelineFrom(index, should_remove_selection = false) {
  if (should_remove_selection) {
    removeSelectionFromStage(stages.value[index], index);
  }
  for (let i = (index + 1); i < stages.value.length; i++) {
    stages.value[i].joins = [...filterPreviousTables(stages.value[i].joins, i)];
    if (should_remove_selection)
      removeSelectionFromStage(stages.value[i], i);
  }
}

function renameAlias(data) {
  const { from, to } = data;
  const stages_clone = cloneDeep(stages.value);
  stages_clone.forEach((stage, stage_index) => {
    // Stage columns
    stage.columns.forEach((column) => {
      if (column.field === from) {
        column.field = to;
        column.label = to;
      }
      if (column.field === constructFieldName({ label: from }, 'results')) {
        column.field = constructFieldName({ label: to }, 'results');
        column.label = constructFieldName({ label: to }, 'results');
      }
      if (column.field === from) {
        column.field = to;
        column.label = to;
      }
      if (column.alias === from) {
        column.alias = to;
      }
      if (column.expr && column.fields.includes(from)) {
        column.fields = column.fields.map(field => field === from ? to : field);
        column.expr = column.expr.replaceAll(`[${from}]`, `[${to}]`);
      }
    });

    // Stage filters
    stage.filters.forEach((filter) => {
      if (filter.meta.column_config.alias === from) {
        filter.field = to;
        filter.meta.column_config.alias = to;
        filter.meta.column_config.field = to;
        filter.meta.column_config.label = to;
      }
    });

    // Stage orderBy
    stage.orderBy.forEach((column) => {
      if (column.column === from) {
        column.column = to;
      }
    });
    // Table columns
    if (stage_index > 0) {
      stage.joins.forEach((join) => {
        if (join.condition) {
          join.condition = join.condition.replaceAll(`[${constructFieldName({ label: from }, 'results')}]`, `[${constructFieldName({ label: to }, 'results')}]`);
        }
        join?.on?.forEach((condition) => {
          if (condition.left === from) {
            condition.left = to;
            condition.left_label = to;
          }
        });
      });
    }
  });
  stages.value = stages_clone;
}

function onFieldsAdded(index) {
  syncPipelineFrom(index);
}

function onFieldsDeleted(index) {
  syncPipelineFrom(index, true);
  stages.value = stages.value.filter((stage, index) => index === 0 || stages.value[index - 1].columns.length);
}

function getJoinConditionText(table) {
  return table.condition ? table.condition : table.on.map(condition => `${condition.logic || ''} ${condition.left_label} ${condition.op || '='} ${condition.right_label}`).join(' ');
}

function getTooltipContent(table) {
  let tooltip_text = '';
  if (table.condition)
    return `<div class="text-xs font-semibold text-gray-700 my-1">${table.condition}</div>`;
  tooltip_text = table.on?.map((condition) => {
    const logic_html = condition.logic ? `<div class="text-xs font-semibold text-gray-700 my-1">${condition.logic}</div>` : '';
    const condition_html = `<div class="text-xs font-medium text-gray-600">${condition.left_label} ${condition.op || '='} ${condition.right_label}</div>`;
    return logic_html + condition_html;
  });
  return tooltip_text?.length ? DOMPurify.sanitize(tooltip_text?.join('')) : '';
}

function getTablesForStage(stage, stage_index) {
  let tables = [];
  if (stage.table || stage_index === 0) {
    const table_label = bi_store.all_schema_by_name[stage.table].label;
    tables = [{ label: table_label, name: stage.table, columns: all_schema_columns.value[stage.table] }];
  }
  stage.joins.forEach((join) => {
    const table_label = bi_store.all_schema_by_name[join.table.name].label;
    tables.push({ label: table_label, name: join.table.name, columns: all_schema_columns.value[join.table.name] });
  });
  return tables;
}

const stage_tables = computed(() => stages.value.map((stage, index) => getTablesForStage(stage, index)));

function getStageTables(index) {
  return stage_tables.value[index] || [];
}

// Cache previous-stage fields per stage to avoid re-computing on every render
const stage_previous_fields = computed(() => stages.value.map((_, index) => {
  if (index > 0) {
    const tables = getTablesFromStageFields(index);
    return tables[0].columns;
  }
  return [];
}));

function getStagePreviousFields(index) {
  return stage_previous_fields.value[index] || [];
}

function updateStage(stage, event) {
  stage.columns = event.columns;
  stage.orderBy = event.orderBy;
  stage.filters = event.filters;
  stage.limit = event.limit;
}
</script>

<template>
  <div class="w-full h-full">
    <div v-for="(stage, index) in stages" :key="index" class="mb-2 border-b pb-2 ">
      <div v-if="stages.length > 1" class="flex justify-between items-center mb-2">
        <div class="text-xs font-medium text-gray-500 bg-gray-50 border border-gray-300 px-2 py-1 rounded-md">
          {{ $t('Stage') }} {{ index + 1 }}
        </div>
        <div class="gap-2 flex items-center">
          <bi-query-builder-add-join :stage="stage" :stage-index="index" :tables="getStageTables(index)" :previous-stage-fields="getStagePreviousFields(index)" :all-tables="all_schemas" @save="stage.joins.push($event)" />
          <hawk-button icon type="text" size="xs" :disabled="index === 0" @click="stages.splice(index, 1)">
            <IconHawkTrashThree class="text-gray-500 size-4" />
          </hawk-button>
        </div>
      </div>
      <div v-if="index === 0" class="text-sm text-gray-700 font-semibold flex items-center justify-between gap-1">
        <div class="flex items-center">
          <IconHawkDatabaseTwo class="size-4 mr-1" /> {{ bi_store.all_schema_by_name[stage.table]?.label }}
        </div>
        <div v-if="stages.length === 1">
          <bi-query-builder-add-join :stage="stage" :stage-index="index" :tables="getStageTables(index)" :previous-stage-fields="getStagePreviousFields(index)" :all-tables="all_schemas" @save="stage.joins.push($event)" />
        </div>
      </div>
      <div v-if="stage.joins.length > 0" class="pl-3">
        <div v-for="(join, join_index) in stage.joins" :key="join" class="pl-3  hover:bg-gray-50 rounded-md flex justify-between group">
          <bi-query-builder-add-join :stage="stage" :tables="getStageTables(index)" :previous-stage-fields="getStagePreviousFields(index)" :stage-index="index" :value="cloneDeep(join)" :all-tables="all_schemas" class="flex w-full py-2 relative" @save="stage.joins[join_index] = $event">
            <div class="text-left flex">
              <div class="border-l absolute -translate-y-[105%] border-gray-300 left-3" :class="{ 'h-3': join_index === 0, 'h-8': join_index > 0 }" />
              <div class="rounded-md p-1 w-6 h-6 flex items-center justify-center border">
                <component :is="join_types[join.type]" class=" text-gray-600" />
              </div>
              <div class="w-4 h-0 border-t border-1 border-gray-300 mt-3 mx-2" />
              <div>
                <div class="text-sm font-semibold text-gray-700">
                  {{ join.table.label }}
                </div>
                <div class="text-xs font-medium text-gray-600 mt-2 flex items-center">
                  {{ $t('on') }} <span v-tippy="{ content: getTooltipContent(join), placement: 'bottom', allowHTML: true, theme: 'my-custom-theme' }" class="border rounded-md p-1 truncate max-w-64 inline-block ml-1">{{ getJoinConditionText(join) }}</span>
                </div>
              </div>
            </div>
          </bi-query-builder-add-join>
          <div class="p-2 hidden group-hover:block">
            <IconHawkXClose class="text-gray-500 hover:text-gray-700 cursor-pointer size-4" @click="stage.joins.splice(join_index, 1);removeSelectionFromStage(stage, index);syncPipelineFrom(index, true)" />
          </div>
        </div>
      </div>
      <bi-query-builder-stage :model-value="stage" class="mt-2" :selected-table="stage.table" :stage-index="index" :tables="getStageTables(index)" :previous-stage-fields="getStagePreviousFields(index)" @update:model-value="updateStage(stage, $event)" @fields-deleted="onFieldsDeleted(index)" @fields-added="onFieldsAdded(index)" @rename="renameAlias" />
    </div>
    <div class="mt-2 pt-2 pb-4">
      <hawk-button
        type="light"
        :class="[is_add_stage_disabled ? '!border-primary-200' : '!border-primary-700']"
        block
        :disabled="is_add_stage_disabled" @click="addStage"
      >
        <IconHawkPlus /> {{ $t('Add stage') }}
      </hawk-button>
    </div>
  </div>
</template>

<style>
/* Using the ::v-deep selector */
.tippy-box[data-theme~='my-custom-theme'] {
  background-color: white;
  border: 1px solid #e0e0e0;
}

.tippy-box[data-theme~='my-custom-theme'] .tippy-arrow {
  border-bottom-color: white;
  display:none;
}
</style>
