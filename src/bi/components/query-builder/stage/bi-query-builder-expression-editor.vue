<script setup>
import { defaultExpressions, ExpressionEditor } from '@sensehawk/expression-editor';
import { Validator } from '@vueform/vueform';
import { storeToRefs } from 'pinia';
import { useBIColumnPicker } from '~/bi/composables/useBIColumPicker';

import { useBiStore } from '~/bi/store/bi.store';
import { useCommonImports } from '~/common/composables/common-imports.composable';
import '@sensehawk/expression-editor/style.css';

const props = defineProps({
  tables: {
    type: Array,
    default: () => [],
  },
  fields: {
    type: Array,
    default: () => [],
  },
  previousStageFields: {
    type: Array,
    default: () => [],
  },
  value: {
    type: Object,
  },
});

const emit = defineEmits(['close', 'save', 'rename']);
const { $t } = useCommonImports();
const form$ = ref(null);

const bi_store = useBiStore();
const { widget_builder_config, all_schema_columns } = storeToRefs(bi_store);
const { stages } = toRefs(widget_builder_config.value.query);

const editor_ref = ref(null);
const expression = ref(props.value?.expr || '');
const { columns } = useBIColumnPicker(props, 'expression');

async function save() {
  await form$.value.validate();
  await editor_ref.value.validateCurrentExpression();
  if (!form$.value.invalid && editor_ref.value.isValid) {
    const alias_map = columns.value.reduce((acc, column) => {
      acc[column.label] = column;
      return acc;
    }, {});
    const aggregations_map = Object.values(defaultExpressions).flat().filter(obj => obj.allowed_in.includes('aggregations')).reduce((acc, agg) => {
      acc[agg.name.toLowerCase()] = agg;
      return acc;
    }, {});
    const has_field_aggregations = editor_ref.value.fields.some(field => alias_map[field]?.is_aggregation);
    const has_function_aggregations = editor_ref.value.functionsUsed.some(fn => !!aggregations_map[fn.toLowerCase()]);
    const is_aggregation = has_field_aggregations || has_function_aggregations;
    emit('save', {
      label: form$.value.data.name,
      field_type: editor_ref.value.outputType,
      expr: expression.value,
      type: is_aggregation ? 'aggregation' : 'column',
      is_aggregation,
      alias: form$.value.data.name,
      fields: editor_ref.value.fields,
    });
  }
  if (props.value && props.value?.label !== form$.value.data.name)
    emit('rename', { from: props.value.label, to: form$.value.data.name });
}

const all_stage_columns = computed(() => stages.value.map(stage => stage.columns).flat());

const uniqueName = class extends Validator {
  get message() {
    return 'Name already exists';
  }

  check(value) {
    const list = all_stage_columns.value.map(({ alias }) => alias).filter(alias => alias !== props.value?.label);
    const selected_table = stages.value[0].table;
    const selected_table_columns = all_schema_columns.value[selected_table];
    selected_table_columns.forEach(column => list.push(column.alias));
    if (list.length > 1 && value) {
      const is_match = list.filter(name => name === value?.trim());
      if (is_match.length > 0)
        return false;
    }
    return true;
  }
};

function onFromMount() {
  form$.value.validate();
  form$.value.elements$.name.input.focus();
}
</script>

<template>
  <div v-click-outside="() => emit('close')">
    <div class="fixed min-w-[640px] min-h-[300px] z-20 bg-white border rounded-lg shadow-lg flex flex-col left-72 top-32">
      <div class="w-full font-medium p-4 flex items-center justify-between border-b">
        <div class="flex items-center gap-2">
          {{ $t('Custom column expression') }}
        </div>
        <div class="flex items-center gap-2" @click="emit('close')">
          <hawk-button icon type="text" size="xs">
            <IconHawkXClose class="text-gray-500 size-4" />
          </hawk-button>
        </div>
      </div>
      <div class="flex-1 p-4">
        <vueform
          ref="form$"
          size="sm"
          :display-errors="false"
          :columns="{
            default: { container: 12, label: 12, wrapper: 12 },
            sm: { container: 12, label: 12, wrapper: 12 },
            md: { container: 12, label: 12, wrapper: 12 },
          }"
          @mounted="onFromMount"
        >
          <TextElement name="name" :label="$t('Name')" :default="value?.label" :rules="['required', uniqueName]" />
        </vueform>
        <div class="mt-4 text-sm text-gray-700 font-medium">
          {{ $t('Expression') }}
        </div>
        <ExpressionEditor
          ref="editor_ref"
          v-model="expression"
          class="mt-1"
          :columns="columns"
          :filter-expressions="['aggregations', 'columns']"
        />
        <div class="text-red-700 text-sm">
          {{ editor_ref?.errors?.[0]?.message }}
        </div>
      </div>
      <div class="flex w-full items-center justify-between p-4 border-t">
        <div />
        <div class="flex items-center gap-2">
          <hawk-button type="outlined" @click="emit('close')">
            {{ $t('Cancel') }}
          </hawk-button>
          <hawk-button :disabled="form$?.invalid || !editor_ref?.isValid" @click="save">
            {{ $t('Save') }}
          </hawk-button>
        </div>
      </div>
    </div>
  </div>
</template>
