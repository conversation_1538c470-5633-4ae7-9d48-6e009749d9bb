<script setup>
import { ExpressionEditor } from '@sensehawk/expression-editor';
import { ref } from 'vue';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';
import { useBiStore } from '~/bi/store/bi.store';

const props = defineProps({
  stage: {
    type: Object,
    default: () => ({}),
  },
  tables: {
    type: Array,
    default: () => [],
  },
  allTables: {
    type: Array,
    default: () => [],
  },
  previousStageFields: {
    type: Array,
    default: () => [],
  },
  stageIndex: {
    type: Number,
  },
  value: {
    type: Object,
  },
});

const emit = defineEmits(['save']);
const form$ = ref(null);
const $t = inject('$t');
const { getIconsForType } = useBIQueryBuilder();
const form_data = ref(null);
const is_menu_open = ref(false);
const join_types = getIconsForType('joins');
const bi_store = useBiStore();

const conditions = ref([{ label: '=', uid: '=' }, { label: '!=', uid: '!=' }, { label: '>', uid: '>' }, { label: '<', uid: '<' }, { label: '>=', uid: '>=' }, { label: '<=', uid: '<=' }]);

const types = ref([{ label: 'Inner Join', description: $t('Keep only the rows that match in both tables.'), uid: 'inner', icon: join_types.inner }, { label: 'Left Join', uid: 'left', description: $t('Keep all rows from the first table, and bring in matching rows from second table.'), icon: join_types.left }, { label: 'Right Join', uid: 'right', description: $t('Keep all rows from the second table, and bring in matching rows from first table.'), icon: join_types.right }, { label: 'Outer Join', uid: 'outer', description: $t('Keep all rows from both tables, matching where possible.'), icon: join_types.outer }]);
const selected_type = ref(types.value[0]);
const expression = ref(props.value?.condition || '');
const is_expression = ref(!!props.value?.condition);
const is_loading = ref(false);
const editor_ref = ref(null);

if (props.value) {
  form_data.value = {
    secondary_dataset: props.allTables.find(table => table.name === props.value.table.name),
    on: props.value.on,
  };
  selected_type.value = types.value.find(type => type.uid === props.value.type);
}

async function save(form$, close) {
  is_loading.value = true;
  try {
    await form$.validate();
    if (form$.invalid)
      return;
    if (is_expression.value) {
      await editor_ref.value.validateCurrentExpression();
      if (!editor_ref.value.isValid)
        return;
    }
    const data = form$.data;
    await bi_store.getTableColumns({ table_name: data?.secondary_dataset?.name, prefix_alias: true });
    emit('save', {
      table: {
        name: data?.secondary_dataset?.name,
        label: data?.secondary_dataset?.label,
      },
      type: selected_type.value.uid,
      condition: is_expression?.value ? expression.value : undefined,
      on: data.on,
    });
    close();
    form$.reset();
    expression.value = '';
    is_expression.value = false;
  }
  finally {
    is_loading.value = false;
  }
}

const selectedTables = computed(() => props.tables.filter(table => table.name !== props.value?.table?.name));

const all_selected_columns = computed(() => selectedTables.value.map(table => table.columns.map(column => ({ label: column.alias, name: column.field, type: column.type, path: column.path }))).flat());
const columns = computed(() => [...all_selected_columns.value, ...(form_data.value?.secondary_dataset?.columns || []).map(column => ({ ...column, name: column.field, label: column.alias }))]);

const selected_tables_names = computed(() => selectedTables.value.map(table => table.name));
const tables = computed(() => props.allTables.filter(table => !selected_tables_names.value.includes(table.name)));
function secondaryDatasetChanged() {
  (form$.value.elements$?.on?.children$Array || []).forEach((el$) => {
    el$.children$.right.reset();
  });
}

function onMenuOpened() {
  is_menu_open.value = true;
}

function onMenuClosed() {
  is_menu_open.value = false;
  if (props.value) {
    form_data.value = {
      secondary_dataset: props.allTables.find(table => table.name === props.value.table.name),
      on: props.value.on,
    };
    expression.value = props.value?.condition;
    is_expression.value = !!props.value?.condition;
    selected_type.value = types.value.find(type => type.uid === props.value.type);
  }
  else {
    form_data.value = null;
    is_expression.value = false;
    expression.value = '';
    selected_type.value = types.value[0];
  }
}

const secondary_dataset_table = computed(() => {
  if (form$.value?.data?.secondary_dataset) {
    return [form$.value.data.secondary_dataset];
  }
  return [];
});

function rightColumnSelected(event, index) {
  const right_child_nodes = form$.value.elements$.on.children$Array[index].children$;
  right_child_nodes.right.update(event.field);
  right_child_nodes.right_label.update(event.label);
  right_child_nodes.right_table.update(event.table_name);
}

function leftColumnSelected(event, index) {
  const left_child_nodes = form$.value.elements$.on.children$Array[index].children$;
  left_child_nodes.left.update(event.table_name ? event.field : event.alias);
  left_child_nodes.left_label.update(event.label);
  left_child_nodes.left_table.update(event.table_name || 'Results');
}

function disableAddConditionButton() {
  const on = form$.value?.data?.on;
  if (on?.length)
    return (on.length && on?.[on.length - 1]?.left && on?.[on.length - 1]?.right);
}
</script>

<template>
  <hawk-menu position="fixed" additional_trigger_classes="w-full flex ring-transparent" @close="onMenuClosed" @open="onMenuOpened">
    <template #trigger>
      <slot>
        <hawk-button
          size="xxs" color="gray" type="light" class="!rounded-md !shadow-none !ring-0"
          :class="{
            '!bg-gray-700 !text-white': is_menu_open,
            '!bg-transparent ': !is_menu_open,
          }"
          icon
        >
          <IconHawkVectorJoin class="size-4" />
        </hawk-button>
      </slot>
    </template>
    <template #content="{ close }">
      <div class="z-20 bg-white border w-[600px] flex flex-col" @keydown.stop @keypress.stop>
        <Vueform ref="form$" v-model="form_data" sync size="sm" :display-errors="false">
          <div class="col-span-12">
            <div class="w-full font-medium p-4 flex items-center justify-between border-b ">
              <div class="flex items-center gap-2">
                {{ $t('Join datasets') }}
              </div>
              <div class="flex items-center gap-2" @click="close">
                <hawk-button icon type="text">
                  <IconHawkXClose class="text-gray-500 size-4" />
                </hawk-button>
              </div>
            </div>
            <div class="flex-1 p-4">
              <div class="col-span-12">
                <div class="text-sm font-medium">
                  {{ $t('Join') }}
                </div>
                <div class="grid grid-cols-12 items-center mt-2">
                  <SelectElement
                    name="primary_dataset" :items="[{ label: stage.table || 'Results' }]"
                    :default="stage.table || 'Results'"
                    :can-clear="false"
                    value-prop="label"
                    disabled
                    object
                    :columns="{
                      default: { container: 5 },
                      sm: { container: 5 },
                      md: { container: 5 },
                      lg: { container: 5 },
                    }"
                  />
                  <hawk-menu position="fixed" class="col-span-2 text-center" :items="types" @select="selected_type = $event">
                    <template #trigger>
                      <hawk-button type="text" icon>
                        <component :is="selected_type.icon" class="size-5" />
                      </hawk-button>
                    </template>
                    <template #item="{ item }">
                      <div class="flex">
                        <component :is="item.icon" class="size-5 mr-2 mt-[1px]" />
                        <div class="text-left max-w-[260px] break-words">
                          <div class="text-sm font-medium text-gray-700">
                            {{ item.label }}
                          </div>
                          <div class="text-xs font-regular text-gray-600">
                            {{ item.description }}
                          </div>
                        </div>
                      </div>
                    </template>
                  </hawk-menu>
                  <SelectElement
                    name="secondary_dataset"
                    :items="tables"
                    value-prop="name"
                    rules="required"
                    object
                    :can-clear="false"
                    :native="false"
                    :columns="{
                      default: { container: 5 },
                      sm: { container: 5 },
                      md: { container: 5 },
                      lg: { container: 5 },
                    }"
                    @change="secondaryDatasetChanged"
                  />
                </div>
                <div v-show="form$?.data?.secondary_dataset" class="mt-4" :class="{ 'max-h-[260px] overflow-y-scroll scrollbar': !is_expression }">
                  <div class="flex  flex justify-between items-center">
                    {{ $t('Conditions') }}
                    <div class="flex">
                      <hawk-button type="text" icon size="xs" class="hover:!bg-gray-100" :class="{ 'bg-gray-200': !is_expression }" @click="is_expression = false">
                        <IconHawkList class="text-gray-600 size-4" />
                      </hawk-button>
                      <hawk-button type="text" icon size="xs" class="hover:!bg-gray-100" :class="{ 'bg-gray-200': is_expression }" @click="is_expression = true">
                        <IconHawkFormula class="text-gray-600 size-4" />
                      </hawk-button>
                    </div>
                  </div>
                  <div v-show="is_expression">
                    <ExpressionEditor
                      ref="editor_ref"
                      v-model="expression"
                      class="mt-1 text-gray-700 text-sm font-normal"
                      :columns="columns"
                      :filter-expressions="['filters']"
                    />
                    <div class="text-red-700 text-sm">
                      {{ editor_ref?.errors?.[0]?.message }}
                    </div>
                  </div>
                  <div v-if="is_expression">
                    <HiddenElement name="condition" meta="true" :value="expression" />
                  </div>
                  <ListElement
                    v-else
                    name="on" :add-text="`+ ${$t('Add condition')}`" :presets="['repeatable_list']" rules="required" min="1"
                    :override-classes="{
                      ListElement: {
                        listItem: 'flex w-full p-2',
                        remove: ['h-5 w-5 ml-auto mt-auto mb-4'],
                        add: [!disableAddConditionButton() ? 'opacity-50  pointer-events-none' : ''],
                      },
                    }"
                  >
                    <template #default="{ index }">
                      <ObjectElement
                        :embed="true" :name="index"
                        :override-classes="{
                          ObjectElement: {
                            container: 'flex-1',
                          },
                        }"
                      >
                        <div v-if="index === 0" class=" text-sm font-medium">
                          {{ $t('on') }}
                        </div>
                        <div v-else class="grid items-center max-w-[100px] ">
                          <HiddenElement name="logic" :meta="true" default="AND" />
                          <hawk-menu
                            position="fixed"
                            :items="[
                              {
                                value: 'AND',
                                label: $t('AND'),
                              },
                              {
                                value: 'OR',
                                label: $t('OR'),
                              },
                            ]"
                            @select="form$.elements$.on.children$Array[index].children$.logic.update($event.value)"
                          >
                            <template #trigger>
                              <div class="flex items-center">
                                <div class="text-sm font-medium">
                                  {{ form$?.data?.on?.[index]?.logic }}
                                </div>
                                <div class="ml-2">
                                  <IconHawkChevronDown class="size-4" />
                                </div>
                              </div>
                            </template>
                          </hawk-menu>
                        </div>
                        <div class="grid grid-cols-12 col-span-12 items-center mt-2">
                          <HiddenElement name="left" meta="true" :value="form$?.data?.on?.[index]?.left" />
                          <HiddenElement name="left_label" meta="true" :value="form$?.data?.on?.[index]?.left_label" />
                          <HiddenElement name="left_table" meta="true" :value="form$?.data?.on?.[index]?.left_table" />
                          <bi-query-builder-joins-field class="col-span-5" :tables="selectedTables" :previous-stage-fields="previousStageFields" :stage-index="stageIndex" @selected="leftColumnSelected($event, index)">
                            <template v-if="form$?.data?.on?.[index]?.left" #default>
                              <div class="text-gray-500 text-xs">
                                {{ form$?.data?.on?.[index]?.left_table }}
                              </div>
                              <div> {{ form$?.data?.on?.[index]?.left_label }} </div>
                            </template>
                          </bi-query-builder-joins-field>

                          <hawk-menu position="fixed" class="col-span-2 text-center" :items="conditions" @select="form$.elements$.on.children$Array[index].children$.op.update($event.uid)">
                            <template #trigger>
                              <hawk-button type="text" icon>
                                {{ form$?.data?.on?.[index]?.op || '=' }}
                              </hawk-button>
                            </template>
                          </hawk-menu>
                          <HiddenElement name="op" meta="true" :value="form$?.data?.on?.[index]?.op" default="=" />
                          <HiddenElement name="right" meta="true" :value="form$?.data?.on?.[index]?.right" />
                          <HiddenElement name="right_label" meta="true" :value="form$?.data?.on?.[index]?.right_label" />
                          <HiddenElement name="right_table" meta="true" :value="form$?.data?.on?.[index]?.right_table" />
                          <bi-query-builder-joins-field class="col-span-5" :tables="secondary_dataset_table" :stage-index="stageIndex" @selected="rightColumnSelected($event, index)">
                            <template v-if="form$?.data?.on?.[index]?.right" #default>
                              <div class="text-gray-500  text-xs">
                                {{ form$?.data?.on?.[index]?.right_table }}
                              </div>
                              <div> {{ form$?.data?.on?.[index]?.right_label }} </div>
                            </template>
                          </bi-query-builder-joins-field>
                        </div>
                      </ObjectElement>
                    </template>
                  </ListElement>
                </div>
              </div>
            </div>
            <div class="flex w-full items-center justify-between p-4 border-t">
              <div />
              <div class="flex items-center gap-2">
                <hawk-button type="outlined" @click="close">
                  {{ $t('Cancel') }}
                </hawk-button>
                <hawk-button
                  name="submit"
                  :loading="is_loading"
                  @click="save(form$, close)"
                >
                  {{ $t('Save') }}
                </hawk-button>
              </div>
            </div>
          </div>
        </Vueform>
      </div>
    </template>
  </hawk-menu>
</template>
