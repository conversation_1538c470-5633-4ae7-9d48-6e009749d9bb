<script setup>
import { ExpressionEditor } from '@sensehawk/expression-editor';
import { useBIColumnPicker } from '~/bi/composables/useBIColumPicker';
import { useCommonImports } from '~/common/composables/common-imports.composable';
import '@sensehawk/expression-editor/style.css';

const props = defineProps({
  tables: {
    type: Array,
    default: () => [],
  },
  fields: {
    type: Array,
    default: () => [],
  },
  previousStageFields: {
    type: Array,
    default: () => [],
  },
  existingExpression: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['close', 'save']);
const { $t } = useCommonImports();

const editor_ref = ref(null);
const type = ref('filters');
const expression = ref(props.existingExpression);
const { columns } = useBIColumnPicker(props, 'expression');

async function save() {
  await editor_ref.value.validateCurrentExpression();
  if (editor_ref.value.isValid) {
    emit('save', {
      expr: expression.value,
    });
  }
}
</script>

<template>
  <div v-click-outside="() => emit('close')">
    <div class="fixed min-w-[640px] min-h-[300px] z-20 bg-white border rounded-lg shadow-lg flex flex-col left-[33rem] top-48">
      <div class="w-full font-medium p-4 flex items-center justify-between border-b">
        <div class="flex items-center gap-2">
          {{ $t('Custom filter expression') }}
        </div>
        <div class="flex items-center gap-2" @click="emit('close')">
          <hawk-button icon type="text" size="xs">
            <IconHawkXClose class="text-gray-500 size-4" />
          </hawk-button>
        </div>
      </div>
      <div class="flex-1 p-4">
        <div class="mt-4 text-sm text-gray-700 font-medium">
          {{ $t('Expression') }}
        </div>
        <ExpressionEditor
          ref="editor_ref"
          v-model="expression"
          class="mt-1"
          :columns="columns"
          :filter-expressions="[type]"
        />
        <div class="text-red-700 text-sm">
          {{ editor_ref?.errors?.[0]?.message }}
        </div>
      </div>
      <div class="flex w-full items-center justify-between p-4 border-t">
        <div />
        <div class="flex items-center gap-2">
          <hawk-button type="outlined" @click="emit('close')">
            {{ $t('Cancel') }}
          </hawk-button>
          <hawk-button :disabled="!editor_ref?.isValid" @click="save">
            {{ $t('Save') }}
          </hawk-button>
        </div>
      </div>
    </div>
  </div>
</template>
