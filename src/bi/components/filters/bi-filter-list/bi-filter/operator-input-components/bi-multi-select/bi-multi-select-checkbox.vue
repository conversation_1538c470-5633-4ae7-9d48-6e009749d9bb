<script setup>
// --------------------------------- Imports -------------------------------- //
import { useIntersectionObserver } from '@vueuse/core';
import { uniq } from 'lodash-es';
import { useBiStore } from '~/bi/store/bi.store';

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  columnConfig: {
    type: Object,
    default: () => ({}),
  },
  componentConfig: {
    type: Object,
    default: () => ({}),
  },
  filterConfig: {
    type: Object,
    default: () => ({}),
  },
});
// ---------------------------------- Emits --------------------------------- //

// ---------------------------- Injects/Provides ---------------------------- //

// ----------------------- Variables - Pinia - consts ----------------------- //
const bi_store = useBiStore();
// --------------------- Variables - Pinia - storeToRefs -------------------- //

// ------------------- Variables - Local - consts and lets ------------------ //

// ------------------------ Variables - Local - refs ------------------------ //
const loadingTrigger = ref(null);
const search = ref('');
const is_column_options_loading = ref(true);
const current_offset = ref(0);
const page_size = ref(50);
const is_loading_more = ref(false);
const has_more_data = ref(true);
const column_options = ref(uniq([
  ...(props.filterConfig?.operator_value || []),
  ...(props.columnConfig?.stats?.distinctValues || []),
]));

// ---------------------- Variables - Local - reactives --------------------- //

// --------------------------- Computed properties -------------------------- //
const checkbox_options = computed(() => {
  const options = column_options.value || [];
  return options;
});

const show_select_all = computed(() => {
  return checkbox_options.value.length > 1;
});
// -------------------------------- Functions ------------------------------- //
function toggleSelectAll(new_val, _old_val, el$) {
  if (new_val) {
    el$.form$.elements$.operator_value.checkAll();
  }
  else {
    el$.form$.elements$.operator_value.uncheckAll();
  }
}

async function fetchColumnValues(query = {}, append = false) {
  if (append) {
    is_loading_more.value = true;
  }
  else {
    is_column_options_loading.value = true;
    current_offset.value = 0;
  }

  const response = await bi_store.getColumnValues({
    table_name: props.columnConfig.table_name,
    column_name: props.columnConfig.name,
    query: {
      ...query,
      limit: page_size.value,
      offset: append ? current_offset.value : 0,
    },
  });

  const { values, pagination } = response;

  if (append) {
    column_options.value = [...column_options.value, ...values];
    // remove duplicate column values from the list
    column_options.value = column_options.value.filter((value, index, self) => self.indexOf(value) === index);

    is_loading_more.value = false;
  }
  else {
    column_options.value = values;
    is_column_options_loading.value = false;
  }

  // Update pagination state based on API response
  has_more_data.value = pagination?.hasMore || false;
  current_offset.value = pagination?.offset + pagination?.currentCount || 0;

  return values;
}

async function loadMoreData() {
  if (is_loading_more.value || !has_more_data.value)
    return;

  await fetchColumnValues({ search: search.value }, true);
}

async function onSearch(val) {
  const column_values = await fetchColumnValues({ search: val });
  column_options.value = column_values;
}

// Intersection Observer for infinite scroll
const { stop } = useIntersectionObserver(
  loadingTrigger,
  ([{ isIntersecting }]) => {
    if (isIntersecting && has_more_data.value && !is_loading_more.value && checkbox_options.value.length > 0) {
      loadMoreData();
    }
  },
  {
    threshold: 0.1,
    rootMargin: '50px',
  },
);

// -------------------------------- Watchers -------------------------------- //

// ----------------------------- Lifecycle Hooks ---------------------------- //
onUnmounted(() => {
  stop();
});
// console.log('Checkbox Operator Component Loaded', props);
</script>

<template>
  <div>
    <template v-if="!columnConfig.is_table_field">
      <ListElement
        class="max-h-48 scrollbar"
        name="operator_value"
        :controls="{ add: true, remove: true, sort: false }"
        :min="1"
        :initial="1"
        :add-classes="{
          ListElement: {
            add: ['hidden'],
            listItem: ['!flex'],
            remove: ['!ml-0 !flex-none'],
          },
        }"
        :default="filterConfig?.operator_value || [null]"
        :presets="['repeatable_list']"
        @keydown.space.stop
        @keydown.enter.stop
      >
        <template #default="{ index }">
          <TextElement
            class="!grow p-1"
            :name="index"
            :placeholder=" $t('Enter value')"
            v-bind="componentConfig"
            input-type="text"
            autocomplete="off"
          />
        </template>
      </ListElement>
    </template>
    <template v-else>
      <div class="col-span-12">
        <HawkSearchInput
          v-model="search"
          :placeholder="$t('Search')"
          :focus-on-mount="true"
          class="px-2"
          :additional-classes="{
            ElementLayout: {
              innerWrapper: 'border-x-0 border-b border-t',
            },
            TextElement: {
              inputContainer: 'border-none',
            },
          }"
          :remove-classes="{
            TextElement: {
              inputContainer_default: 'focused:form-ring',
              inputContainer_success: 'focused:form-ring',
            },
          }"
          :icon-class="['!h-4.5', '!w-4.5']"
          :debounce_time="700"
          full_width
          @update:model-value="onSearch"
          @keydown.stop
        />
        <span v-if="column_options?.length === 0 && !is_column_options_loading" class="text-xs text-gray-400 col-span-12 px-2">{{ $t('No results found') }}</span>
      </div>
      <div class="scrollbar max-h-60">
        <CheckboxElement
          v-if="show_select_all" name="select_all" :default="filterConfig?.select_all || false"
          :add-classes="{
            CheckboxElement: {
              wrapper: 'px-1 py-2 gap-2',
            },
          }"
          @change="toggleSelectAll"
        >
          {{ $t('Select all') }}
        </CheckboxElement>
        <CheckboxgroupElement
          rules="required" name="operator_value" :items="checkbox_options" :default="filterConfig?.operator_value || []"
          :add-classes="{
            CheckboxgroupCheckbox: {
              container: 'px-1 py-2 gap-2',
            },
            ElementError: {
              container: 'hidden',
            },
          }"
        />

        <!-- Intersection Observer Trigger -->
        <div v-if="has_more_data" ref="loadingTrigger" class="h-1 w-full" />

        <!-- Loading Indicator -->
        <HawkLoader v-if="is_loading_more" class="col-span-12 !m-2" />
      </div>
    </template>
  </div>
</template>
