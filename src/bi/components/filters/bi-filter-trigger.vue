<script setup>
import BIFilter from '~/bi/components/filters/bi-filter-list/bi-filter/bi-filter.vue';
import { useBIColumnPicker } from '~/bi/composables/useBIColumPicker';

const props = defineProps({
  tables: {
    type: Array,
    required: true,
  },
  stageIndex: {
    type: Number,
  },
  previousStageFields: {
    type: Array,
    default: () => [],
  },
  fields: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['apply']);

const is_expression_dropdown_open = ref(false);
const filter_column_picker_ref = ref(null);

function onApply($event, close = () => {}) {
  emit('apply', $event);
  updateDropdownPosition();
  close?.();
}

const { stages } = useBIColumnPicker(props, 'filters');

function onFilterCancel() {
  filter_column_picker_ref.value?.resetHoveredColumn();
}
onMounted(() => {
});

const dropdown = ref(null);
function updateDropdownPosition() {
  if (dropdown.value) {
    nextTick(() => {
      dropdown.value.setFixedPosition();
    });
  }
}
</script>

<template>
  <hawk-menu ref="dropdown" position="fixed" additional_trigger_classes="!ring-0 !focus:ring-0 !px-0 !py-0" additional_dropdown_classes="transition duration-150 ease-in-out">
    <template #trigger="{ open }">
      <div>
        <slot>
          <Hawk-button
            size="xxs" color="gray" type="light" class="border !border-gray-200 !rounded-md"
            :class="{
              '!bg-gray-700 !text-white': open,
            }"
            icon
          >
            <slot name="trigger_label" />
          </Hawk-button>
        </slot>
      </div>
    </template>
    <template #content="{ close }">
      <div>
        <div>
          <bi-query-builder-column-picker
            ref="filter_column_picker_ref"
            :stages="stages"
            :stage-index="stageIndex"
            :is-filters="true"
            :show-stage-header="false"
            slot-container-classes="!min-h-0 w-[350px]"
            @expression="is_expression_dropdown_open = true"
          >
            <template #default="{ column, table }">
              <BIFilter v-if="column" :key="column" :selected-column-config="{ ...column, table_name: table.name }" @cancel="onFilterCancel" @apply="($event) => onApply($event, () => { close() })" />
            </template>
          </bi-query-builder-column-picker>
        </div>
        <bi-filter-expression-editor v-if="is_expression_dropdown_open" :tables="tables" :fields="[]" @save="($event) => onApply($event, () => { is_expression_dropdown_open = false; close() })" @close="() => { is_expression_dropdown_open = false; close() }" />
      </div>
    </template>
  </hawk-menu>
</template>
