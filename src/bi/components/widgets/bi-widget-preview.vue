<script setup>
import { storeToRefs } from 'pinia';
import BiBottomDrawer from '~/bi/components/common/bi-bottom-drawer.vue';
import { BI_ACTUAL_CHARTS } from '~/bi/constants/bi-constants';
import { useBiStore } from '~/bi/store/bi.store';

const props = defineProps({
  widgetName: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['continue']);

const bi_store = useBiStore();
const { widget_builder_config, widget_data, is_chart_builder_form_valid, table_metadata } = storeToRefs(bi_store);

const state = reactive({
  last_parsed_config: null,
});

const is_chart_empty = computed(() => {
  return [...BI_ACTUAL_CHARTS, 'number_chart'].includes(widget_builder_config.value.chart.type) && !is_chart_builder_form_valid.value;
});

async function onPublishWidget() {
  let config = {};
  if (widget_builder_config.value.chart.type === 'table') {
    const {
      columns_map = {},
      show_row_headers = false,
      conditional_formatting = [],
    } = widget_builder_config.value.chart;
    config = {
      type: 'table',
      columns_map,
      show_row_headers,
      conditional_formatting,
    };
  }
  else if (widget_builder_config.value.chart.type === 'pivot_table') {
    const {
      pivot_rows = [],
      pivot_columns = [],
      pivot_values = [],
      show_row_totals = false,
      show_column_totals = false,
      show_grand_total = false,
      conditional_formatting = [],
    } = widget_builder_config.value.chart;
    config = {
      type: 'pivot_table',
      pivot_rows,
      pivot_columns,
      pivot_values,
      show_row_totals,
      show_column_totals,
      show_grand_total,
      conditional_formatting,
    };
  }
  else if (widget_builder_config.value.chart.type === 'number_chart') {
    config = widget_builder_config.value.chart;
  }
  else if (BI_ACTUAL_CHARTS.includes(widget_builder_config.value.chart.type)) {
    config = state.last_parsed_config;
  }
  emit('continue', config);
}

watch(() => widget_builder_config.value.chart, (new_val, old_val) => {
  console.log('🆘 ~ with chart:', new_val?.type, old_val?.type);
}, { deep: true });

watch(() => widget_builder_config.value, (new_val, old_val) => {
  console.log('🆘 ~ without  chart:', new_val?.chart?.type, old_val?.chart?.type);
}, { deep: true });
</script>

<template>
  <div class="h-full w-full flex flex-col relative">
    <div
      class="p-6 flex-1"
      :class="{
        'flex justify-center items-center': widget_builder_config.chart.type === 'number_chart',
      }"
    >
      <div v-if="is_chart_empty" class="h-full w-full flex flex-col justify-center items-center text-center">
        <IconIllustrationBiEmptyChartBuilder />
        <div class="text-sm font-semibold text-gray-900 mt-4 mb-1">
          No chart to show
        </div>
        <div class="text-sm font-normal text-gray-600">
          Configure the chart from left panel to start generating the chart
        </div>
      </div>
      <BiWidget
        v-else
        widget-id="preview"
        :config="widget_builder_config"
        :widget-data="widget_data"
        :widget-name="props.widgetName"
        :table-metadata="table_metadata"
        @last-parsed-config="state.last_parsed_config = $event"
      />
    </div>

    <hr>
    <div class="p-6 w-full flex justify-end items-center">
      <HawkButton :disabled="is_chart_empty" @click="onPublishWidget">
        <IconHawkRocketTwo />
        Publish widget
      </HawkButton>
    </div>

    <BiBottomDrawer
      show-text="Show results"
      hide-text="Hide results"
    >
      <template #default>
        TABLE
      </template>
    </BiBottomDrawer>
  </div>
</template>
