<script setup>
import { watchDebounced } from '@vueuse/core';
import chroma from 'chroma-js';
import { cloneDeep, isEqual, uniq } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { nextTick, onBeforeUnmount, reactive } from 'vue';
import { useModal } from 'vue-final-modal';
import BiCreateRichTextWidget from '~/bi/components/bi-create-rich-text-widget.vue';
import BiCreateWidget from '~/bi/components/bi-create-widget.vue';
import BiTablePreview from '~/bi/components/widgets/table-widgets/bi-table-preview.vue';
import { useBiChartBuilderHelpers } from '~/bi/composables/bi-chart-builder-helpers.composable';
import { useBiExport } from '~/bi/composables/bi-export.composable';
import { BI_ACTUAL_CHARTS } from '~/bi/constants/bi-constants';
import { useBiStore } from '~/bi/store/bi.store';
import { generateChart } from '~/bi/utils/bi-helper.utils.js';
import { sleep } from '~/common/utils/common.utils';
import { csvInjectionProtector } from '~/common/utils/common.utils.js';

const props = defineProps({
  // 'preview' for the preview mode
  widgetId: {
    type: String,
    required: true,
  },
  // Required for both preview and dashboard grid mode
  config: {
    type: Object,
    required: true,
  },
  // Only required for the preview mode
  widgetData: {
    type: Array,
    required: true,
  },
  // Only required for the preview mode
  tableMetadata: {
    type: Object,
    required: true,
  },
  // Required in dashboard grid mode (ALWAYS), Required for the preview mode if the chart type is number_chart
  widgetName: {
    type: String,
    required: true,
  },
  // Only required for the dashboard grid mode and tells if the grid is in editing mode
  isEditing: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['lastParsedConfig']);

const HawkWysiwygEditorComponent = defineAsyncComponent(() => import('~/common/components/organisms/hawk-wysiwyg-editor/hawk-wysiwyg-editor.vue'));

const bi_store = useBiStore();
const { current_grid_layout } = storeToRefs(bi_store);

const { convertFormDataToConfig } = useBiChartBuilderHelpers();
const { exportWidget } = useBiExport();

const state = reactive({
  resize_observer: null,
  table_resize_observer: null,
  data: [],
  is_loading: false,
  table_metadata: {},
  show_table_widget: false,
  chart_render_count: 0,
  table_widget_key: 0,
  table_height: 0,
  show_click_actions: false,
  click_position: { x: 0, y: 0 },
  clicked_data: null,
  scroll_listener: null,
});

let resize_timeout = null;
let chart_instance = null;
let chart_library = null;

const edit_widget = useModal({
  component: BiCreateWidget,
  attrs: {
    onClose() {
      edit_widget.close();
    },
  },
});

const edit_rich_text_widget = useModal({
  component: BiCreateRichTextWidget,
  attrs: {
    onClose() {
      edit_rich_text_widget.close();
    },
  },
});

const card_styles = computed(() => {
  const styles = {};
  if (props.config.chart.type === 'number_chart') {
    if (props.config.chart.number_widget_color === '#FFFFFF') {
      styles['background-color'] = '#FFFFFF';
    }
    else {
      const base = chroma(props.config.chart.number_widget_color || '#FFFFFF');
      const targetLuminance = base.luminance() > 0.6 ? 0.4 : 0.9;

      const contrastColor = base.luminance(targetLuminance);
      const hex = contrastColor.hex();
      // return chroma(props.config.chart.number_widget_color || '#FFFFFF').brighten(2).hex();
      styles['background-color'] = hex;
    }
  }
  else if (props.config.chart.type === 'rich_text' && !props.config.chart.is_transparent_background) {
    styles['background-color'] = props.config.chart.background_color;
  }
  return styles;
});

const download_options = computed(() => {
  return [
    {
      label: 'Export as PDF',
      on_click: async () => {
        exportWidget(props.widgetId, props.widgetName, 'pdf');
      },
    },
    {
      label: 'Export as CSV',
      on_click: () => {
        exportTableAsCSV();
      },
    },
    {
      label: 'Export as PNG',
      on_click: async () => {
        exportWidget(props.widgetId, props.widgetName, 'png');
      },
    },
  ];
});

function exportTableAsCSV() {
  let allowed_fields = [];
  if (['table'].includes(props.config.chart.type)) {
    allowed_fields = Object.keys(props.config.chart.columns_map).filter(col => props.config.chart.columns_map[col].visible);
  }
  else if (props.config.chart.type === 'pivot_table') {
    allowed_fields = [
      ...(props.config.chart.pivot_rows || []),
      ...(props.config.chart.pivot_columns || []),
      ...(props.config.chart.pivot_values || []),
    ];
  }
  else if (props.config.chart.type === 'number_chart') {
    allowed_fields = [props.config.chart.layout_category, ...props.config.chart.layout_values.map(item => item.value)];
  }
  else {
    allowed_fields = [
      props.config.chart.data.category,
      ...(props.config.chart.data.values || []),
      ...(props.config.chart.data.stackBy !== true ? [props.config.chart.data.stackBy] : []),
    ].filter(Boolean);
  }
  allowed_fields = uniq(allowed_fields);

  const headers = allowed_fields.map(key => `"${csvInjectionProtector((key || '').toString())}"`).join(',');
  const csv_data = state.data
    .map(row => allowed_fields.map(f => `"${csvInjectionProtector((row[f] ?? '').toString())}"`).join(','))
    .join('\n');

  const blob = new Blob([`${headers}\n${csv_data}`], { type: 'text/csv;charset=utf-8;' });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${props.widgetName}.csv`;
  a.click();
}

function handleChartClick(eventData, chartLibrary) {
  const existingMenus = document.querySelectorAll('[data-chart-action-menu]');
  existingMenus.forEach((menu) => {
    const event = new CustomEvent('close-chart-menu');
    menu.dispatchEvent(event);
  });

  state.clicked_data = eventData;

  let clickX, clickY;

  if (chartLibrary === 'echarts') {
    const mouseEvent = eventData.event?.event;
    if (mouseEvent) {
      mouseEvent.stopPropagation();
      mouseEvent.preventDefault();
      clickX = mouseEvent.clientX;
      clickY = mouseEvent.clientY;
    }
    else {
      const chartElement = document.getElementById(props.widgetId);
      const rect = chartElement.getBoundingClientRect();
      clickX = rect.left + rect.width / 2;
      clickY = rect.top + rect.height / 2;
    }
  }
  else if (chartLibrary === 'fusioncharts') {
    if (eventData.dataObj && eventData.dataObj.clientX && eventData.dataObj.clientY) {
      clickX = eventData.dataObj.clientX;
      clickY = eventData.dataObj.clientY;
    }
    else {
      const chartElement = document.getElementById(props.widgetId);
      const rect = chartElement.getBoundingClientRect();
      clickX = rect.left + rect.width / 2;
      clickY = rect.top + rect.height / 2;
    }
  }

  const menuHeight = 80;
  const menuWidth = 160;
  const windowHeight = window.innerHeight;
  const windowWidth = window.innerWidth;
  const margin = 10;

  const spaceBelow = windowHeight - clickY;
  const showBelow = spaceBelow >= menuHeight + margin;

  let finalX = clickX;
  if (clickX + menuWidth / 2 > windowWidth - margin) {
    finalX = windowWidth - menuWidth / 2 - margin;
  }
  else if (clickX - menuWidth / 2 < margin) {
    finalX = menuWidth / 2 + margin;
  }

  state.click_position = {
    x: finalX,
    y: clickY,
    showBelow,
  };

  setTimeout(() => {
    state.show_click_actions = true;

    state.scroll_listener = () => {
      hideClickActions();
    };
    window.addEventListener('scroll', state.scroll_listener, true);
  }, 0);
}

function hideClickActions() {
  state.show_click_actions = false;
  state.clicked_data = null;

  if (state.scroll_listener) {
    window.removeEventListener('scroll', state.scroll_listener, true);
    state.scroll_listener = null;
  }
}

function handleContainerClick(event) {
  if (event.target === event.currentTarget) {
    hideClickActions();
  }
}

function handleDrillToDetail() {
  logger.log('Drill to detail clicked', state.clicked_data);
  hideClickActions();
}

function handleSetAsFilter() {
  logger.log('Set as filter clicked', state.clicked_data);
  hideClickActions();
}

function editWidget(view) {
  edit_widget.patchOptions({
    attrs: {
      mode: 'edit',
      view,
      widgetName: props.widgetName,
      config: cloneDeep(props.config),
      widgetData: state.data,
      tableMetadata: state.table_metadata,
      widgetId: props.widgetId,
      onClose() {
        edit_widget.close();
      },
    },
  });
  edit_widget.open();
}

function editRichTextWidget() {
  edit_rich_text_widget.patchOptions({
    attrs: {
      mode: 'edit',
      chartConfig: props.config.chart,
      widgetId: props.widgetId,
      onClose() {
        edit_rich_text_widget.close();
      },
    },
  });
  edit_rich_text_widget.open();
}

async function duplicateWidget() {
  const widget_id = crypto.randomUUID();

  const current_widget_position = current_grid_layout.value.find(widget => widget.widget_id === props.widgetId);
  const new_x = current_widget_position.x;
  const new_y = current_widget_position.y + current_widget_position.h;

  bi_store.createWidget(
    widget_id,
    {
      config: props.config,
      name: `${props.widgetName} - Copy`,
    },
    { x: new_x, y: new_y, w: current_widget_position.w, h: current_widget_position.h },
  );

  await sleep(500);
  const el = document.querySelectorAll(`[widget_id='${widget_id}']`)?.[0];
  if (el) {
    el.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'nearest',
    });
  }
}

function deleteWidget() {
  bi_store.deleteWidget(props.widgetId);
}

async function renderWidget() {
  if (['table', 'pivot_table'].includes(props.config.chart.type)) {
    if (props.widgetId !== 'preview') {
      await nextTick();
      state.table_height = calculateTableHeight();
    }
    state.show_table_widget = true;
  }
  // This weird check is needed because in the preview mode, the chart type has a suffix of _chart. We can't convert the form data to config in the preview file because we need chart_instance as well.
  else if ([...BI_ACTUAL_CHARTS, ...BI_ACTUAL_CHARTS.map(chart => chart.replace('_chart', ''))].includes(props.config.chart.type)) {
    let config = props.config.chart;
    if (props.widgetId === 'preview') {
      config = await convertFormDataToConfig(props.config, chart_instance);
      emit('lastParsedConfig', config);
    }
    const generated_chart = await generateChart(props.widgetId, state.data, config, chart_instance);
    if (!generated_chart)
      return;
    chart_instance = generated_chart.chart_instance;
    chart_library = generated_chart.chart_library;

    if (chart_library === 'echarts') {
      chart_instance.on('click', (params) => {
        logger.log('echarts click', params);
        handleChartClick(params, 'echarts');
      });
    }
    else if (chart_library === 'fusioncharts') {
      chart_instance.addEventListener('dataPlotClick', (eventObj, dataObj) => {
        logger.log('dataPlotClick', eventObj, dataObj);
        handleChartClick({ eventObj, dataObj }, 'fusioncharts');
      });
    }
    state.chart_render_count++;
  }
}

function handleResizeEnd() {
  if (props.widgetId !== 'preview' && ['table', 'pivot_table'].includes(props.config.chart.type)) {
    state.table_height = calculateTableHeight();
    state.table_widget_key++;
  }
}

function calculateTableHeight() {
  if (props.widgetId === 'preview')
    return 0;

  const containerElement = document.getElementById(`container-${props.widgetId}`);
  if (!containerElement)
    return 0;

  const containerRect = containerElement.getBoundingClientRect();
  const headerHeight = 32;
  const bottomSpacing = 28;

  return Math.max(0, containerRect.height - headerHeight - bottomSpacing);
}

watchDebounced(
  () => props.config,
  async (new_config, old_config) => {
    if (
      !props.widgetId
      || !props.config.query
      || !props.config.chart
      || (new_config.chart?.type === 'rich_text')
      || (props.widgetId !== 'preview' && isEqual(new_config, old_config))
    ) {
      return;
    }
    state.is_loading = true;
    if (new_config?.chart?.type !== old_config?.chart?.type) {
      state.show_table_widget = false;
    }
    if (props.widgetId === 'preview') {
      state.data = props.widgetData;
      state.table_metadata = props.tableMetadata;
    }
    else {
      const data = await bi_store.getWidgetData(props.config.query.selected_database, props.config.query.stages);
      state.data = data.data;
      state.table_metadata = data.metadata;
    }
    state.is_loading = false;
    await nextTick();
    renderWidget();
  },
  { deep: true, immediate: true, debounce: props.widgetId === 'preview' ? 300 : 0 },
);

// Resize handler (only in the grid mode)
watch(() => state.chart_render_count, async () => {
  if (!state.is_loading && props.widgetId !== 'preview') {
    await nextTick();
    const chartElement = document.getElementById(props.widgetId);
    state.resize_observer = new ResizeObserver(() => {
      if (chart_library === 'echarts') {
        chart_instance?.resize?.();
      }
      else if (chart_library === 'fusioncharts') {
        const { width, height } = chartElement.getBoundingClientRect();
        chart_instance?.resizeTo?.(width, height);
      }
    });
    state.resize_observer.observe(chartElement);
  }
});

watch(() => [state.show_table_widget, props.widgetId], async () => {
  if (state.show_table_widget && props.widgetId !== 'preview' && ['table', 'pivot_table'].includes(props.config.chart.type)) {
    await nextTick();
    const containerElement = document.getElementById(`container-${props.widgetId}`);
    if (containerElement) {
      state.table_resize_observer = new ResizeObserver(() => {
        if (resize_timeout) {
          clearTimeout(resize_timeout);
        }

        resize_timeout = setTimeout(() => {
          handleResizeEnd();
          resize_timeout = null;
        }, 500);
      });
      state.table_resize_observer.observe(containerElement);
    }
  }
}, { immediate: true });

onBeforeUnmount(() => {
  if (chart_instance) {
    chart_instance.dispose();
    chart_instance = null;
  }

  if (state.resize_observer) {
    state.resize_observer.disconnect();
    state.resize_observer = null;
  }

  if (state.table_resize_observer) {
    state.table_resize_observer.disconnect();
    state.table_resize_observer = null;
  }

  if (resize_timeout) {
    clearTimeout(resize_timeout);
    resize_timeout = null;
  }

  if (state.scroll_listener) {
    window.removeEventListener('scroll', state.scroll_listener, true);
    state.scroll_listener = null;
  }
});
</script>

<template>
  <div
    :id="`container-${props.widgetId}`"
    class="h-full w-full group scrollbar relative"
    :class="{
      'p-0.5': props.widgetId === 'preview',
      'p-3 bg-white border border-gray-200 rounded': props.widgetId !== 'preview' || props.config.chart.type === 'number_chart',
      '!border-transparent !bg-transparent': props.config.chart.type === 'rich_text' && props.config.chart.is_transparent_background,
      'h-fit !p-0 !pr-2': props.config.chart.type === 'rich_text',
      '!w-fit !h-fit min-w-[300px]': props.config.chart.type === 'number_chart' && props.widgetId === 'preview',
    }"
    v-bind="{
      style: card_styles,
    }"
    @click="handleContainerClick"
  >
    <div v-if="(props.widgetId !== 'preview' || props.config.chart.type === 'number_chart') && props.config.chart.type !== 'rich_text'" class="w-[calc(100%-8px)] h-6 flex items-center justify-between gap-3 mb-2">
      <div class="w-full text-sm font-medium text-gray-900 text-ellipsis overflow-hidden whitespace-nowrap widget-title">
        {{ props.widgetName }}
      </div>
      <div v-if="props.isEditing" class="items-center gap-3 group-hover:flex hidden">
        <IconHawkEditFive
          v-tippy="{
            content: 'Edit data',
            placement: 'top',
          }"
          class="w-4 h-4 text-gray-600 outline-0 cursor-pointer"
          @click="editWidget('data-builder')"
        />
        <IconHawkBarChartTen
          v-tippy="{
            content: 'Edit chart',
            placement: 'top',
          }"
          class="w-4 h-4 text-gray-600 outline-0 cursor-pointer"
          @click="editWidget('chart-builder')"
        />
        <IconHawkCopyFour
          v-tippy="{
            content: 'Duplicate',
            placement: 'top',
          }"
          class="w-4 h-4 text-gray-600 outline-0 cursor-pointer"
          @click="duplicateWidget"
        />
        <IconHawkTrashThree
          v-tippy="{
            content: 'Delete',
            placement: 'top',
          }"
          class="w-4 h-4 text-gray-600 outline-0 cursor-pointer"
          @click="deleteWidget"
        />
      </div>
      <div v-else-if="props.widgetId !== 'preview'" class="items-center gap-3 group-hover:flex hidden">
        <HawkMenu additional_trigger_classes="!ring-0 !border-0 mt-1.5" position="bottom-left" :items="download_options">
          <template #trigger>
            <IconHawkDotsVertical
              class="w-4 h-4 text-gray-600 outline-0 cursor-pointer"
            />
          </template>
        </HawkMenu>
      </div>
    </div>
    <HawkLoader v-if="state.is_loading" />
    <div v-else-if="['table', 'pivot_table'].includes(props.config.chart.type)" class="overflow-hidden">
      <BiTablePreview
        v-if="state.show_table_widget"
        v-bind="{
          ...(props.widgetId !== 'preview' && state.table_height > 0 ? {
            height: state.table_height,
          } : {}),
        }"
        :id="props.widgetId"
        :key="props.config.chart.type"
        :is-builder="false"
        :config="props.config"
        :data="state.data"
        :table-metadata="state.table_metadata"
        :is-editing="props.widgetId === 'preview'"
      />
    </div>
    <div v-else-if="props.config.chart.type === 'rich_text'">
      <div class="relative group">
        <HawkWysiwygEditorComponent
          :model-value="props.config.chart.content"
          :editor_enabled="false"
          :display_images_fullwidth="true"
          :menu_enabled="false"
          :resize_config="{ quality: 1, maxWidth: 1280, maxHeight: 800 }"
          :plugins="['tables']"
          :editor_classes="['py-1']"
        />
        <div
          v-if="props.isEditing"
          class="absolute top-0 -right-2 gap-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex bg-white border rounded shadow-sm p-1"
        >
          <IconHawkEditFive
            v-tippy="{
              content: 'Edit',
              placement: 'top',
            }"
            class="w-4 h-4 text-gray-600 outline-0 cursor-pointer hover:text-gray-800"
            @click="editRichTextWidget"
          />
          <IconHawkCopyFour
            v-tippy="{
              content: 'Duplicate',
              placement: 'top',
            }"
            class="w-4 h-4 text-gray-600 outline-0 cursor-pointer hover:text-gray-800"
            @click="duplicateWidget"
          />
          <IconHawkTrashThree
            v-tippy="{
              content: 'Delete',
              placement: 'top',
            }"
            class="w-4 h-4 text-gray-600 outline-0 cursor-pointer hover:text-gray-800"
            @click="deleteWidget"
          />
        </div>
      </div>
    </div>
    <BiNumberChartWidget
      v-else-if="props.config.chart.type === 'number_chart'"
      :id="props.widgetId"
      :data="state.data"
      :config="props.config"
      :is-preview="props.widgetId === 'preview'"
      @open-drilldown="openDrilldownPopup($event)"
    />
    <div v-else :id="props.widgetId" class="h-[calc(100%-32px)] w-full" />

    <Teleport to="body">
      <div
        v-if="state.show_click_actions"
        v-click-outside="() => { state.show_click_actions = false }"
        data-chart-action-menu
        class="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-1 min-w-[160px]"
        :style="{
          left: `${state.click_position.x}px`,
          top: `${state.click_position.y}px`,
          transform: state.click_position.showBelow
            ? 'translate(-50%, 10px)'
            : 'translate(-50%, calc(-100% - 10px))',
        }"
        @click.stop
        @close-chart-menu="state.show_click_actions = false"
      >
        <button
          class="w-full p-3 text-left text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 flex items-center gap-2"
          @click="handleDrillToDetail"
        >
          Drill to detail
        </button>
        <button
          class="w-full p-3 text-left text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 flex items-center gap-2"
          @click="handleSetAsFilter"
        >
          Set as filter
        </button>
      </div>
    </Teleport>
  </div>
</template>
